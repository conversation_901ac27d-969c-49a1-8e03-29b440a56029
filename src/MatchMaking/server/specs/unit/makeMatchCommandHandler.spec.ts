import {describe, it, expect, beforeEach} from 'vitest';
import {MakeMatchCommandHandler} from '@/src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler';
import {InMemoryMatchmakingQueueRepository} from '@/src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository';
import {InMemoryMatchRepository} from '@/src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository';
import {MatchmakingQueueItem} from '@/src/server/domain/MatchmakingQueue/MatchmakingQueueItem';
import {createFakeContext} from '@/src/server/specs/helpers/fakes/FakeContext';

let context: ReturnType<typeof createFakeContext>;
let queueRepository: InMemoryMatchmakingQueueRepository;
let matchRepository: InMemoryMatchRepository;
let handler: MakeMatchCommandHandler;

beforeEach(() => {
  context = createFakeContext();
  queueRepository = new InMemoryMatchmakingQueueRepository();
  matchRepository = new InMemoryMatchRepository();
  handler = new MakeMatchCommandHandler(context, queueRepository, matchRepository);
});

describe('MakeMatchCommandHandler', () => {
  describe('When an opponent is available', () => {
    it('should create a match and dispatch an event', async () => {
      // Arrange
      const opponent = MatchmakingQueueItem.create({gameId: 'g1', deckId: 'd1', playerId: 'u1', queuedAt: Date.now()});
      await queueRepository.save(opponent);

      // Act
      await handler.handle({gameId: 'g1', playerId: 'u2', queueId: 'q1'});

      // Assert
      const event = context.recorded[0].event as {type: string; payload: {matchId: string}};
      const matchId = event.payload.matchId;
      const match = await matchRepository.findById(matchId);
      expect(match?.getPlayers()).toEqual(['u1','u2']);
      expect(event.type).toBe('MatchCreated');
    });
  });

  describe('When no opponent is available', () => {
    it('should not create a match', async () => {
      // Act
      await handler.handle({gameId: 'g1', playerId: 'u1', queueId: 'q1'});

      // Assert
      expect(context.recorded).toHaveLength(0);
      const size = ((matchRepository as unknown) as {items: Map<string, unknown>}).items.size;
      expect(size).toBe(0);
    });
  });
});
