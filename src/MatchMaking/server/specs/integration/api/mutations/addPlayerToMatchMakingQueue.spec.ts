import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/server/specs/helpers/createAppUsers";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  MIKE_APP_USER,
  MIKE_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "../../../helpers/fakes/fakeUsers";
import {GOBELIN_DES_BOIS, LORCANA} from "../../../helpers/fakes/fakeGames";
import {getAllFrom} from "@/src/server/specs/helpers/getAllFrom";
import {createFakeDeck} from "@/src/server/specs/helpers/createFakeDeck";

describe('addPlayerToMatchMakingQueue', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;
  let asMike: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);
    asMike = testConvex.withIdentity(MIKE_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
    createAppUser(asAdmin, MIKE_APP_USER);
  });

  describe('When the user is not already in the match making queue', () => {
    it('should add the user with his deck to the queue', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: JOHN_APP_USER.appUserId}, asAdmin);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const matchMakingQueueItems = await getAllFrom('matchmakingQueue', asAdmin);
      expect(matchMakingQueueItems).toHaveLength(1);

      const matchMakingQueueItem = matchMakingQueueItems[0];
      expect(matchMakingQueueItem.gameId).toBe(lorcanaId);
      expect(matchMakingQueueItem.playerId).toBe(JOHN_APP_USER.appUserId);
      expect(matchMakingQueueItem.deckId).toBe(deckId);
    });

    it('should update the player status to "waiting-for-opponent"', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: JOHN_APP_USER.appUserId}, asAdmin);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const appUsers = await getAllFrom('appUsers', asAdmin);
      const johnAppUser = appUsers.find(appUser => appUser.appUserId === JOHN_APP_USER.appUserId);
      expect(johnAppUser?.status).toBe('waiting-for-opponent');
    });
  });

  describe('When the user is already in the match making queue', () => {
    it('should not add the user twice', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: JOHN_APP_USER.appUserId}, asAdmin);
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const matchMakingQueueItems = await getAllFrom('matchmakingQueue', asAdmin);
      expect(matchMakingQueueItems).toHaveLength(1);

      const matchMakingEvents = await getAllFrom('matchmakingEvents', asAdmin);
      expect(matchMakingEvents).toHaveLength(1);
    });
  });

  describe('When an other user is already waiting for the same game in the queue', () => {
    it('should create a match with both users', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);
      const gobelinDesBoisId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);
      await addPlayerToMatchMakingQueue(asMike, deckId, gobelinDesBoisId);
      await addPlayerToMatchMakingQueue(sophie, deckId, lorcanaId);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const matches = await getAllFrom('matches', asAdmin);
      expect(matches[0].players).toEqual([SOPHIE_APP_USER.appUserId, JOHN_APP_USER.appUserId]);
      expect(matches).toHaveLength(1);
    });

    it('should remove both users from the queue', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);
      const gobelinDesBoisId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);
      await addPlayerToMatchMakingQueue(asMike, deckId, gobelinDesBoisId);
      await addPlayerToMatchMakingQueue(sophie, deckId, lorcanaId);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const matchMakingQueueItems = await getAllFrom('matchmakingQueue', asAdmin);
      expect(matchMakingQueueItems).toHaveLength(1);
      const remainingQueueItem = matchMakingQueueItems.pop()!;
      expect(remainingQueueItem.playerId).toBe(MIKE_APP_USER.appUserId);
    });

    it('should update both players status to "playing"', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      const deckId = await createFakeDeck({ name: 'Deck 1', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);
      const gobelinDesBoisId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);
      await addPlayerToMatchMakingQueue(asMike, deckId, gobelinDesBoisId);
      await addPlayerToMatchMakingQueue(sophie, deckId, lorcanaId);

      // Act
      await addPlayerToMatchMakingQueue(john, deckId, lorcanaId);

      // Assert
      const appUsers = await getAllFrom('appUsers', asAdmin);
      const sophieAppUser = appUsers.find(appUser => appUser.appUserId === SOPHIE_APP_USER.appUserId);
      const johnAppUser = appUsers.find(appUser => appUser.appUserId === JOHN_APP_USER.appUserId);
      expect(sophieAppUser?.status).toBe('playing');
      expect(johnAppUser?.status).toBe('playing');
    });
  });

  function addPlayerToMatchMakingQueue(currentUser: TestConvexForDataModel<DataModel>, deckId: string, gameId: string) {
    return currentUser.mutation(api.mutations.addPlayerToMatchMakingQueue.endpoint, {gameId, deckId});
  }
});