import {Context} from "@/src/server/application/ports/Context";
import {MakeMatchCommand} from "./MakeMatchCommand";
import {MatchmakingQueueRepository} from "@/src/server/application/ports/MatchmakingQueueRepository";
import {MatchRepository} from "@/src/server/application/ports/MatchRepository";
import {Match} from "@/src/server/domain/Match/Match";

export class MakeMatchCommandHandler {
  private readonly context: Context;
  private readonly queueRepository: MatchmakingQueueRepository;
  private readonly matchRepository: MatchRepository;

  constructor(
    context: Context,
    queueRepository: MatchmakingQueueRepository,
    matchRepository: MatchRepository
  ) {
    this.context = context;
    this.queueRepository = queueRepository;
    this.matchRepository = matchRepository;
  }

  async handle({gameId, playerId, queueId}: MakeMatchCommand) {
    const queue = await this.queueRepository.findByGameId(gameId);

    const availableOpponents = queue.findOpponentsOf(playerId);
    if (availableOpponents.length === 0) return;

    const opponent = availableOpponents[0];
    const players = [opponent.getPlayerId(), playerId];

    const matchId = await this.matchRepository.save(
      Match.create({
        gameId,
        players,
        status: 'setup',
      })
    );

    await this.context.dispatchMatchmakingEvent(gameId, queueId, {
      type: "MatchCreated",
      payload: {
        matchId,
        players,
      },
    });
  }
}