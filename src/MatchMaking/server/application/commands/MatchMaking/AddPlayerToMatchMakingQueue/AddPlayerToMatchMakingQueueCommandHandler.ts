import {Context} from "@/src/server/application/ports/Context";
import {AddPlayerToMatchMakingQueueCommand} from "./AddPlayerToMatchMakingQueueCommand";
import {MatchmakingQueueRepository} from "@/src/server/application/ports/MatchmakingQueueRepository";
import {MatchmakingQueueItem} from "@/src/server/domain/MatchmakingQueue/MatchmakingQueueItem";
import {TimeService} from "@/src/server/application/ports/TimeService";
export class AddPlayerToMatchMakingQueueCommandHandler {
  private readonly context: Context
  private readonly repository: MatchmakingQueueRepository
  private readonly time: TimeService
  constructor(context: Context, repository: MatchmakingQueueRepository, time: TimeService) {
    this.context = context
    this.repository = repository
    this.time = time
  }

  async handle({gameId, deckId, userId}: AddPlayerToMatchMakingQueueCommand) {
    const queue = await this.repository.findByGameId(gameId);
    if (queue.hasPlayer(userId)) return;

    const queueId = await this.repository.save(
      MatchmakingQueueItem.create({
        gameId,
        deckId,
        playerId: userId,
        queuedAt: this.time.now(),
      })
    );

    await this.context.dispatchMatchmakingEvent(gameId, queueId, {
      type: "PlayerAddedToMatchMakingQueue",
      payload: {
        playerId: userId,
        gameId,
        deckId,
        queueId,
      },
    });
  }
}
