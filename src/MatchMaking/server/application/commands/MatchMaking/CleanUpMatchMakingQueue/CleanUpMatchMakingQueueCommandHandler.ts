import {Context} from "@/src/server/application/ports/Context";
import {CleanUpMatchMakingQueueCommand} from "./CleanUpMatchMakingQueueCommand";
import {MatchRepository} from "@/src/server/application/ports/MatchRepository";
import {MatchmakingQueueRepository} from "@/src/server/application/ports/MatchmakingQueueRepository";

export class CleanUpMatchMakingQueueCommandHandler {
  private readonly context: Context;
  private readonly matchRepository: MatchRepository;
  private readonly queueRepository: MatchmakingQueueRepository;

  constructor(
    context: Context,
    matchRepository: MatchRepository,
    queueRepository: MatchmakingQueueRepository
  ) {
    this.context = context
    this.matchRepository = matchRepository;
    this.queueRepository = queueRepository;
  }

  async handle({matchId, players}: CleanUpMatchMakingQueueCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) return;

    const gameId = match.getGameId();

    const queue = await this.queueRepository.findByGameId(gameId);
    const matchedItems = queue.findItemsForPlayers(players);

    for (const item of matchedItems) {
      await this.queueRepository.remove(item.getId());

      await this.context.dispatchMatchmakingEvent(gameId, item.getId(), {
        type: "PlayerRemovedFromQueue",
        payload: {
          playerId: item.getPlayerId(),
          reason: "matched",
        },
      });
    }
  }
}