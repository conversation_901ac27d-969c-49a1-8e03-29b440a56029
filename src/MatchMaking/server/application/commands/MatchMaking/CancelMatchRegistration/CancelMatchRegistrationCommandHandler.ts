import {Context} from "@/src/server/application/ports/Context";
import {CancelMatchRegistrationCommand} from "./CancelMatchRegistrationCommand";
import {MatchmakingQueueRepository} from "@/src/server/application/ports/MatchmakingQueueRepository";
export class CancelMatchRegistrationCommandHandler {
  private readonly context: Context;
  private readonly repository: MatchmakingQueueRepository;

  constructor(context: Context, repository: MatchmakingQueueRepository) {
    this.context = context;
    this.repository = repository;
  }

  async handle({gameId, userId}: CancelMatchRegistrationCommand) {
    const queue = await this.repository.findByGameId(gameId);
    const item = queue.findItemByPlayer(userId);
    if (!item) return;

    await this.repository.remove(item.getId());

    await this.context.dispatchMatchmakingEvent(gameId, item.getId(), {
      type: "PlayerRemovedFromQueue",
      payload: {
        playerId: userId,
        reason: "cancelled",
      },
    });

    await this.context.dispatchMatchmakingEvent(gameId, item.getId(), {
      type: "PlayerRegistrationCancelled",
      payload: {
        playerId: userId,
      },
    });
  }
}
