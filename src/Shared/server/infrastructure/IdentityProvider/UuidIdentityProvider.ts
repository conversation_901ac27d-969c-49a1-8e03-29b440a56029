import {IdentityProvider} from "@/src/server/application/ports/IdentityProvider";
import {CryptoPort} from "@/src/server/application/ports/CryptoPort";

export class UuidIdentityProvider implements IdentityProvider {
    private readonly crypto: CryptoPort;

    constructor(crypto: CryptoPort) {
        this.crypto = crypto;
    }

    generateId(): string {
        return this.crypto.randomUUID();
    }
}