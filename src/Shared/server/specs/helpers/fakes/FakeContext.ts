import {Context, DomainEvent} from "@/src/server/application/ports/Context";

export interface FakeContext extends Context {
  recorded: {type: 'match' | 'matchmaking'; gameId: string; id: string; event: DomainEvent}[];
}

export function createFakeContext(): FakeContext {
  const recorded: FakeContext['recorded'] = [];
  return {
    recorded,
    async dispatchMatchEvent(gameId: string, matchId: string, event: DomainEvent) {
      recorded.push({type: 'match', gameId, id: matchId, event});
    },
    async dispatchMatchmakingEvent(gameId: string, aggregateId: string, event: DomainEvent) {
      recorded.push({type: 'matchmaking', gameId, id: aggregateId, event});
    }
  };
}
