import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";
import {createFakeGame} from "@/src/server/specs/helpers/createFakeGame";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/server/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "../../../helpers/fakes/fakeUsers";
import {LORCANA} from "../../../helpers/fakes/fakeGames";

describe('loadGameById', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  const NOT_EXISTING_GAME_ID = 'kn7d9mbgfv6j7zng3zr3a35dh17g5wk4';

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When the game does not exist', () => {
    it('should not load any game information', async () => {
      // Act
      const result = await loadGameById(asSophie, NOT_EXISTING_GAME_ID);

      // Assert
      expect(result.data).toBeNull();
    });

    it('should return an error', async () => {
      // Act
      const result = await loadGameById(asSophie, NOT_EXISTING_GAME_ID);

      // Assert
      expect(result.error).toBe('Game not found');
    });
  });

  describe('When the game exists', () => {
    it('should load its information', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);

      // Act
      const result = await loadGameById(asSophie, lorcanaId);

      // Assert
      expect(result.data).toEqual({id: lorcanaId, ...LORCANA});
    });

    it('should not return an error', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);

      // Act
      const result = await loadGameById(asSophie, lorcanaId);

      // Assert
      expect(result.error).toBeNull();
    });
  });

  function loadGameById(currentUser: TestConvexForDataModel<DataModel>, gameId: string) {
    return currentUser.query(api.queries.loadGameById.endpoint, {gameId});
  }
});