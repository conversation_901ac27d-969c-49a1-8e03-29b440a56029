import type {AnyAction, ThunkDispatch} from '@reduxjs/toolkit';
import {
  deckBuilderReducer,
  DeckBuilderState,
  initialDeckBuilderState
} from '@/src/client/domain/DeckBuilder/deckBuilderReducer';
import {catalogReducer, CatalogState, initialCatalogState} from '@/src/client/domain/Catalog/catalogReducer';
import {
  catalogFiltersReducer,
  CatalogFilterState,
  initialCatalogFiltersState
} from '@/src/client/domain/Catalog/catalogFiltersReducer';
import {
  catalogSearchReducer,
  CatalogSearchState,
  initialCatalogSearchState
} from '@/src/client/domain/Catalog/catalogSearchReducer';
import {
  gameSettingsReducer,
  GameSettingsState,
  initialGameSettingsState
} from '@/src/client/domain/GameSettings/gameSettingsReducer';
import type {LocationService} from '../services/LocationService';
import type {DeckDraftService} from '../services/DeckDraftService/DeckDraftService';

export interface RootState {
  deckBuilder: DeckBuilderState;
  catalog: CatalogState;
  catalogFilters: CatalogFilterState;
  catalogSearch: CatalogSearchState;
  gameSettings: GameSettingsState;
}

export type ThunkExtra = {
  locationService: LocationService;
  deckDraftService: DeckDraftService;
};

export type AppDispatch = ThunkDispatch<RootState, ThunkExtra, AnyAction>;

export const rootReducers = {
  deckBuilder: deckBuilderReducer,
  catalog: catalogReducer,
  catalogFilters: catalogFiltersReducer,
  catalogSearch: catalogSearchReducer,
  gameSettings: gameSettingsReducer,
};

export const rootInitialState = {
  deckBuilder: initialDeckBuilderState,
  catalog: initialCatalogState,
  catalogFilters: initialCatalogFiltersState,
  catalogSearch: initialCatalogSearchState,
  gameSettings: initialGameSettingsState,
};
