import {configureStore} from "@reduxjs/toolkit";
import {DeckDraftService} from "@/src/client/application/services/DeckDraftService/DeckDraftService";
import {BrowserLocationService} from "@/src/client/infrastructure/services/location/browserLocationService";
import {BrowserDeckDraftService} from "@/src/client/infrastructure/services/deckDraft/browserDeckDraftService";
import {subscribeToDeckDraft} from "@/src/client/application/subscribers/subscribeToDeckDraft";
import {rootReducers} from "@/src/client/application/store/appStore";

export const locationService = new BrowserLocationService();
export let deckDraftService: DeckDraftService = new BrowserDeckDraftService();
export const setDeckDraftService = (service: DeckDraftService) => {
  deckDraftService = service;
};

export const store = configureStore({
  reducer: rootReducers,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService, deckDraftService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});

subscribeToDeckDraft(store);
