import {FC} from "react";
import {Con<PERSON>er, Flex, Grid, Head<PERSON>} from "@radix-ui/themes";
import {loadGameList} from "@/src/server/application/queries/loadGameList";
import GameDetailsButton from "@/src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton";

const GameListPage: FC = async () => {
  const {data} = await loadGameList();

  return (
    <Container p="3">
      <Flex direction="column" gap="3">
        <Heading size="4">
          Catalog
        </Heading>
        <Grid columns="6" gap="3">
          {data.map((game) => (
            <GameDetailsButton key={game.id} gameId={game.id}>
              {game.name}
            </GameDetailsButton>
          ))}
        </Grid>
      </Flex>
    </Container>
  );
};

export default GameListPage;