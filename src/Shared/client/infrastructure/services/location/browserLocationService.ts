import {LocationService} from "@/src/client/application/services/LocationService";

export class BrowserLocationService implements LocationService {
  getFilters(): string[] {
    if (typeof window === 'undefined') return [];
    const value = new URLSearchParams(window.location.search).get('filters');
    return value ? value.split(',').filter(Boolean) : [];
  }

  setFilters(filters: string[]): void {
    this.updateParam('filters', filters.join(','));
  }

  getSearch(): string {
    if (typeof window === 'undefined') return '';
    return new URLSearchParams(window.location.search).get('search') ?? '';
  }

  setSearch(search: string): void {
    this.updateParam('search', search);
  }

  private updateParam(key: string, value: string) {
    if (typeof window === 'undefined') return;
    const url = new URL(window.location.href);
    if (value) {
      url.searchParams.set(key, value);
    } else {
      url.searchParams.delete(key);
    }
    window.history.replaceState(null, '', url.toString());
  }
}
