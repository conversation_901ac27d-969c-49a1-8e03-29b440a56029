'use client';

import { Button } from '@radix-ui/themes';
import { type ComponentPropsWithoutRef, forwardRef, useState } from 'react';
import './ShiningButton.css';
import {cn} from "@/src/client/infrastructure/lib/utils";

type ShiningButtonProps = ComponentPropsWithoutRef<typeof Button>;

const ShiningButton = forwardRef<HTMLButtonElement, ShiningButtonProps>(({ className, ...props }, ref) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleMouseEnter = () => {
    if (!isAnimating) {
      setIsAnimating(true);
    }
  };

  const handleAnimationEnd = () => {
    setIsAnimating(false);
  };

  return (
    <Button
      ref={ref}
      className={cn('hover:cursor-pointer button-shine-effect', { animate: isAnimating }, className)}
      onMouseEnter={handleMouseEnter}
      onAnimationEnd={handleAnimationEnd}
      {...props}
    />
  );
});

ShiningButton.displayName = 'ShiningButton';

export default ShiningButton;
