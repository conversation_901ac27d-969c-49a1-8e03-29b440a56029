'use client';

import {FC, PropsWithChildren} from "react";
import Link from "next/link";
import {useLocale} from "@/src/client/infrastructure/hooks/useLocale/useLocale";
import {buildGameUrl} from "@/src/client/infrastructure/builders/urlBuilder";
import {Button} from "@radix-ui/themes";

type Props = {
  gameId: string
};

const GameDetailsButton: FC<PropsWithChildren<Props>> = ({gameId, children}) => {
  const locale = useLocale();
  const url = buildGameUrl(locale, gameId);

  return (
    <Button size="3" asChild>
      <Link href={url}>{children}</Link>
    </Button>
  );
};

export default GameDetailsButton;