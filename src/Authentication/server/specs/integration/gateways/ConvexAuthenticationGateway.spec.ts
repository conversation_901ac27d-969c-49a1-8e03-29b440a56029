import type {DataModel} from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import {UserNotAuthenticatedError} from '@/src/server/domain/User/errors/UserNotAuthenticatedError';
import {UserNotRegisteredError} from '@/src/server/domain/User/errors/UserNotRegisteredError';
import {
  ConvexAuthenticationGateway
} from '@/src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway';
import {convexTest, type TestConvexForDataModel} from 'convex-test';
import {SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/specs/helpers/fakes/fakeUsers";

describe('ConvexAuthenticationGateway', () => {
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    const testConvex = convexTest(schema);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
  });

  describe('When the user is not authenticated', () => {
    it('should throw an error', async () => {
      await convexTest(schema).run(async (ctx) => {
        const gateway = new ConvexAuthenticationGateway(ctx);
        await expect(gateway.getAuthenticatedUser()).rejects.toThrow(new UserNotAuthenticatedError());
      });
    });
  });

  describe('When the user is authenticated but not registered', () => {
    it('should throw an error', async () => {
      await asSophie.run(async (ctx) => {
        const gateway = new ConvexAuthenticationGateway(ctx);
        await expect(gateway.getAuthenticatedUser()).rejects.toThrow(new UserNotRegisteredError());
      });
    });
  });

  it('should get the authenticated user from the Convex context', async () => {
    // Arrange
    await asSophie.run(async (ctx) => {
      await ctx.db.insert('appUsers', {
        ...SOPHIE_APP_USER,
        createdAt: Date.now(),
      });

      // Act
      const gateway = new ConvexAuthenticationGateway(ctx);
      const user = await gateway.getAuthenticatedUser();

      // Assert
      expect(user.id()).toEqual(SOPHIE_APP_USER.appUserId);
      expect(user.get('authId')).toEqual(SOPHIE_APP_USER.convexUserId);
      expect(user.get('name')).toEqual(SOPHIE_APP_USER.name);
      expect(user.get('image')).toEqual(SOPHIE_APP_USER.avatar);
      expect(user.get('email')).toEqual(SOPHIE_APP_USER.email);
    });
  });
});
