import {GenericMutationCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {AppUserRepository} from "@/src/server/application/ports/AppUserRepository";
import {AppUser} from "@/src/server/domain/AppUser/AppUser";

export class ConvexAppUserRepository implements AppUserRepository {
  constructor(private readonly ctx: GenericMutationCtx<DataModel>) {}

  async findById(appUserId: string): Promise<AppUser | null> {
    const doc = await this.ctx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', appUserId))
      .unique();
    return doc
      ? AppUser.fromSnapshot({
          id: doc.appUserId,
          authId: doc.convexUserId,
          name: doc.name,
          image: doc.avatar,
          email: doc.email!,
        })
      : null;
  }

  async save(user: AppUser) {
    const data = user.toSnapshot();
    const doc = await this.ctx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', data.id))
      .unique();
    if (doc) {
      await this.ctx.db.patch(doc._id as Id<'appUsers'>, {status: data.status});
    }
  }
}
