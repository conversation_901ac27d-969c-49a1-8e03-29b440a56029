import {AppUser} from "@/src/server/domain/AppUser/AppUser";
import {AuthenticationGateway} from "@/src/server/application/ports/AuthenticationGateway";
import {GenericMutationCtx, GenericQueryCtx} from "convex/server";
import {DataModel} from "@/convex/_generated/dataModel";
import {UserNotAuthenticatedError} from "@/src/server/domain/User/errors/UserNotAuthenticatedError";
import {getOneFrom} from "convex-helpers/server/relationships";
import {UserNotRegisteredError} from "@/src/server/domain/User/errors/UserNotRegisteredError";

export class ConvexAuthenticationGateway implements AuthenticationGateway {
  private readonly ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getAuthenticatedUser(): Promise<AppUser> {
    const identity = await this.ctx.auth.getUserIdentity();
    if (identity === null) {
      throw new UserNotAuthenticatedError();
    }

    const convexId = identity.subject.split('|')[0];

    const user = await getOneFrom(this.ctx.db, 'appUsers', 'by_convexUserId', convexId);

    if (!user) {
      throw new UserNotRegisteredError();
    }

    return AppUser.fromSnapshot({
      id: user.appUserId,
      authId: convexId,
      name: user.name,
      image: user.avatar,
      email: user.email!,
    });
  }
}
