'use client';

import {FC} from "react";
import {Container, Flex, Section} from "@radix-ui/themes";
import SignInForm from "@/src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm";
import {Sparkles} from "@/src/client/infrastructure/components/ui/Sparkles/Sparkles";

type Props = {
  locale: string
};

const SignInPage: FC<Props> = ({locale}) => {
  console.log("locale", locale);

  return (
    <Flex direction="column" width="100%">
      <Section
        size={{initial: '2', md: '3'}}
        style={{
          backgroundImage:
            'linear-gradient(rgba(0, 0, 150, 0.3), rgba(0, 150, 150 , 0.2)), url(/backgrounds/background4.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
        py="0"
      >
        <Container size="3" className="relative">
          <Flex
            direction="column"
            align="center"
            justify="center"
            gap="4"
            style={{
              minHeight: '100svh',
            }}
          >
            <SignInForm/>
          </Flex>
          <Sparkles
            minSize={1}
            maxSize={2}
            particleDensity={20}
            className="top-0 left-0 bottom-0 right-0 absolute z-0 pointer-events-none !opacity-80"
            particleColor="#FFFFFF"
            speed={0.1}
          />
        </Container>
      </Section>
    </Flex>
  );
};

export default SignInPage;