'use client';

import {Dialog, VisuallyHidden} from '@radix-ui/themes';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import Image from 'next/image';
import {useDeckBuilder} from '@/src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder';

export default function DeckCardDetailsDialog() {
  const {cardDetails, hideCardDetails} = useDeckBuilder();

  if (!cardDetails) {
    return null;
  }

  return (
    <Dialog.Root open={!!cardDetails} onOpenChange={hideCardDetails}>
      <Dialog.Content
        style={{padding: 0, width: 'fit-content', height: 'fit-content'}}
        className="!hover:cursor-pointer"
      >
        <DialogPrimitive.Title className="sr-only">
          <VisuallyHidden>{cardDetails.cardName}</VisuallyHidden>
        </DialogPrimitive.Title>
        <DialogPrimitive.Description className="sr-only">
          <VisuallyHidden>Card details</VisuallyHidden>
        </DialogPrimitive.Description>

        <Image
          quality={100}
          src={`/game-assets/cards/en/thumbnail/${cardDetails.cardImage}`}
          alt={cardDetails.cardName}
          width={364}
          height={512}
          className="cursor-pointer"
          onClick={hideCardDetails}
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}
