'use client';

import {Flex, Heading, Text} from "@radix-ui/themes";
import {FC} from "react";
import {useDeckBuilder} from "@/src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder";
import {useGameId} from "@/src/client/infrastructure/hooks/useGameId/useGameId";
import {useGameSettingsByGameId} from "@/src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId";

const TotalCardsInDeckCounter: FC = () => {
  const {totalCardsInDeck} = useDeckBuilder();
  const gameId = useGameId();
  const {settings} = useGameSettingsByGameId({gameId});
  const maxCards = settings?.maxCardsInDeck ?? 60;

  return (
    <Flex direction="column" pl="1">
      <Heading size="4">Your Deck</Heading>
      <Flex align="center" gap="1">
        <Text
          as="span"
          size="2"
          color={totalCardsInDeck > maxCards ? 'red' : 'green'}
        >
          {totalCardsInDeck}
        </Text>
        <Text as="span" size="2" color="gray">
          /{maxCards} cards
        </Text>
      </Flex>
    </Flex>
  );
};

export default TotalCardsInDeckCounter;