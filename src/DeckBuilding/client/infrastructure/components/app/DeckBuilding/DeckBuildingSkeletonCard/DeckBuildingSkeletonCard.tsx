"use client"

import { FC } from "react"
import { Flex, Skeleton, Text } from "@radix-ui/themes"
import ShiningCard from "@/src/client/infrastructure/components/ui/ShiningCard/ShiningCard"

const DeckBuildingSkeletonCard: FC = () => {
  return (
    <ShiningCard disableScaling className="h-full">
      <Flex direction="column" gap="1" style={{ height: "100%" }}>
        <Text size="2" align="center">
          <Skeleton loading width="160px" height="20px" />
        </Text>

        <Flex flexGrow="1" />

        <Flex direction="column" gap="2">
          <Skeleton loading width="150px" height="210px" style={{ alignSelf: "center" }} />

          <Flex justify="center" align="center" gap="2">
            <Skeleton loading width="24px" height="24px" />
            <Skeleton loading width="36px" height="18px" />
            <Skeleton loading width="24px" height="24px" />
          </Flex>
        </Flex>
      </Flex>
    </ShiningCard>
  )
}

export default DeckBuildingSkeletonCard;
