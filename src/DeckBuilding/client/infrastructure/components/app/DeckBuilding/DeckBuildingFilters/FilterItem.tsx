import {FC, memo} from "react";
import {CheckboxCards, Flex, Text} from "@radix-ui/themes";
import {Filter} from "@/src/client/domain/Catalog/Filter";

type FilterItemProps = {
  filter: Filter;
};
export const FilterItem: FC<FilterItemProps> = memo(({filter}) => (
  <CheckboxCards.Item key={filter.name} value={filter.name}>
    <div className="hover:cursor-pointer">
      <Flex align="center" gap="2">
        <Text>{filter.text}</Text>
      </Flex>
    </div>
  </CheckboxCards.Item>
));

FilterItem.displayName = 'FilterItem';
