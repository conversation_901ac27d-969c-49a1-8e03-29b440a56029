import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';
import {useQuery} from 'convex-helpers/react/cache/hooks';
import {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch} from '@/src/client/application/store/appStore';
import {loadGameSettings} from '@/src/client/application/commands/loadGameSettings/loadGameSettings';
import {getGameSettings} from '@/src/client/application/queries/getGameSettings/getGameSettings';
import {isGameSettingsLoading} from '@/src/client/application/queries/isGameSettingsLoading/isGameSettingsLoading';
import {getGameSettingsError} from '@/src/client/application/queries/getGameSettingsError/getGameSettingsError';

export function useGameSettingsByGameId({gameId}: {gameId: string}) {
  const dispatch = useDispatch<AppDispatch>();

  const result = useQuery(api.queries.loadGameSettingsByGameId.endpoint, {
    gameId: gameId as Id<'games'>,
  });

  useEffect(() => {
    dispatch(loadGameSettings(result));
  }, [dispatch, result]);

  return {
    settings: useSelector(getGameSettings),
    isLoading: useSelector(isGameSettingsLoading),
    error: useSelector(getGameSettingsError),
  };
}
