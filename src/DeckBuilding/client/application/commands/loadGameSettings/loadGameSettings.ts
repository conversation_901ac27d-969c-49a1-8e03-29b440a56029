import {AppDispatch} from '@/src/client/application/store/appStore';
import {
  gameSettingsLoadedEvent,
  gameSettingsLoadingFailedEvent,
  gameSettingsLoadingStartedEvent,
} from '@/src/client/domain/GameSettings/gameSettingsEvents';
import {LoadGameSettingsRequest} from './loadGameSettingsRequest';

export const loadGameSettings = (request: LoadGameSettingsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(gameSettingsLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(gameSettingsLoadingFailedEvent({error: request.error}));
    return;
  }

  const settings = {maxCardsInDeck: request.data!.maxCardsInDeck};
  dispatch(gameSettingsLoadedEvent({gameSettings: settings}));
};
