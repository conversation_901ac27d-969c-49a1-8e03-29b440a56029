import {createAsyncThunk} from "@reduxjs/toolkit";

import {AddCardToDeckRequest} from "@/src/client/application/commands/addCardToDeck/addCardToDeckRequest";
import {RootState} from "@/src/client/application/store/appStore";
import {ADD_CARD_TO_DECK_EVENT_TYPE} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/domain/DeckBuilder/DeckBuilder";
import {getCatalogCardById} from "../../queries/getCatalogCardById/getCatalogCardById";

export const addCardToDeck = createAsyncThunk<void, AddCardToDeckRequest, { state: RootState }>(
  ADD_CARD_TO_DECK_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const catalogCard = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);

    deckBuilder.addCard(catalogCard);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);
