import {RootState} from "@/src/client/application/store/appStore";
import {createAsyncThunk} from "@reduxjs/toolkit";
import {SHOW_CARD_DETAILS_EVENT_TYPE} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/domain/DeckBuilder/DeckBuilder";
import {ShowCardDetailsRequest} from "@/src/client/application/commands/showCardDetails/showCardDetailsRequest";
import {getCatalogCardById} from "@/src/client/application/queries/getCatalogCardById/getCatalogCardById";

export const showCardDetails = createAsyncThunk<void, ShowCardDetailsRequest, { state: RootState }>(
  SHOW_CARD_DETAILS_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const card = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);
    deckBuilder.showCardDetails(card);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);