import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState} from "@/src/client/application/store/appStore";
import {deckLoadedEvent} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {getCatalogCardById} from "@/src/client/application/queries/getCatalogCardById/getCatalogCardById";
import {
  LoadDeckIntoBuilderRequest
} from "@/src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilderRequest";

export const loadDeckIntoBuilder = createAsyncThunk<void, LoadDeckIntoBuilderRequest, {state: RootState}>(
  'deckBuilder/loadDeck',
  async ({deck}, {dispatch, getState}) => {
    const state = getState();
    const deckCards = deck.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckLoadedEvent({name: deck.name, cards: deckCards}));
  }
);
