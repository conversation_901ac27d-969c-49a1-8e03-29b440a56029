import {createAsyncThunk} from '@reduxjs/toolkit';
import {searchUpdatedEvent} from '@/src/client/domain/Catalog/catalogSearchEvents';
import {RootState, ThunkExtra} from '@/src/client/application/store/appStore';
import {SearchRequest} from "@/src/client/application/commands/search/searchRequest";

export const search = createAsyncThunk<void, SearchRequest, { state: RootState; extra: ThunkExtra }>(
  'catalog/search',
  async ({search}, {dispatch, extra}) => {
    dispatch(searchUpdatedEvent({search}));
    extra.locationService.setSearch(search);
  }
);
