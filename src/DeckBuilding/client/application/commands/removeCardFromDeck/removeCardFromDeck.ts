import {createAsyncThunk} from "@reduxjs/toolkit";
import {RootState} from "@/src/client/application/store/appStore";
import {
  RemoveCardFromDeckRequest
} from "@/src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest";

import {REMOVE_CARD_FROM_DECK_EVENT_TYPE} from "@/src/client/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/domain/DeckBuilder/DeckBuilder";
import {getCatalogCardById} from "../../queries/getCatalogCardById/getCatalogCardById";

export const removeCardFromDeck = createAsyncThunk<void, RemoveCardFromDeckRequest, { state: RootState }>(
  REMOVE_CARD_FROM_DECK_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const catalogCard = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState(state.deckBuilder);

    deckBuilder.removeCard(catalogCard);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);