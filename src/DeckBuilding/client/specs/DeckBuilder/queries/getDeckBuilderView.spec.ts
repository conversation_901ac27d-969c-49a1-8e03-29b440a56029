import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";
import {getDeckBuilderView} from "@/src/client/application/queries/getDeckBuilderView/getDeckBuilderView";

describe('getDeckBuilderView', () => {
  describe('When the view is catalog', () => {
    it('should return catalog', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const view = getDeckBuilderView(getState());

      // Assert
      expect(view).toBe('catalog');
    });
  });

  describe('When the view is deck', () => {
    it('should return deck', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {view: 'deck'},
      });

      // Act
      const view = getDeckBuilderView(getState());

      // Assert
      expect(view).toBe('deck');
    });
  });
});
