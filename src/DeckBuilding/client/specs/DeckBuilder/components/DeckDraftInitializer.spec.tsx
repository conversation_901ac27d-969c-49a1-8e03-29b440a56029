import {render, act, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {createTestingStore} from '@/src/client/specs/helpers/store/createTestingStore';
import {DeckDraftInitializer} from '@/src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/specs/helpers/factories/fakeCatalogCards';
import {FakeDeckDraftService} from '@/src/client/specs/helpers/fakes/FakeDeckDraftService';

const draft = {
  name: 'Draft',
  cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}],
};

describe('When DeckDraftInitializer mounts', () => {
  it('should load the draft into the deck builder', async () => {
    // Arrange
    const deckDraftService = new FakeDeckDraftService(draft);
    const store = createTestingStore({
      catalog: {
        cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED},
        status: 'success',
      },
    }, {deckDraftService});

    // Act
    await act(async () => {
      render(
        <Provider store={store}>
          <DeckDraftInitializer />
        </Provider>
      );
    });

    // Assert
    await waitFor(() => {
      expect(store.getState().deckBuilder.cardsInDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(2);
    });
  });

  it('should do nothing when no draft exists', async () => {
    // Arrange
    const deckDraftService = new FakeDeckDraftService(null);
    const store = createTestingStore({
      catalog: {
        cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED},
        status: 'success',
      },
    }, {deckDraftService});

    // Act
    await act(async () => {
      render(
        <Provider store={store}>
          <DeckDraftInitializer />
        </Provider>
      );
    });

    // Assert
    expect(store.getState().deckBuilder.cardsInDeck).toEqual({});
  });
});
