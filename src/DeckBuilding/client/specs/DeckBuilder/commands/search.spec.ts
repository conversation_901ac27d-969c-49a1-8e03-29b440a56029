import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";
import {search} from "@/src/client/application/commands/search/search";
import {getSearchTerm} from "@/src/client/application/queries/getSearchTerm/getSearchTerm";
import {FakeLocationService} from "@/src/client/specs/helpers/fakes/FakeLocationService";

describe('search', () => {
  describe('When a search term is provided', () => {
    it('should update the location with the search term', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({}, {locationService});

      // Act
      await dispatch(search({search: 'goofy'}));

      // Assert
      expect(getSearchTerm(getState())).toBe('goofy');
      expect(locationService.getSearch()).toBe('goofy');
    });
  });
});
