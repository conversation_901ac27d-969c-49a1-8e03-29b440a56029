import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";
import {GOOFY_GROUNDBREAKING_CHEF} from "@/src/client/specs/helpers/factories/fakeCatalogCards";
import {hideCardDetails} from "@/src/client/application/commands/hideCardDetails/hideCardDetails";
import {getCardDetails} from "@/src/client/application/queries/getCardDetails/getCardDetails";

describe('hideCardDetails', () => {
  it('should close the dialog', async () => {
    // Arrange
    const {dispatch, getState} = createTestingStore({
      deckBuilder: {
        cardsInDeck: {
          [GOOFY_GROUNDBREAKING_CHEF.id]: {
            card: GOOFY_GROUNDBREAKING_CHEF,
            quantity: 1,
          },
        },
        cardDetailsDisplayed: {
          cardImage: GOOFY_GROUNDBREAKING_CHEF.image,
          cardName: GOOFY_GROUNDBREAKING_CHEF.name,
        },
      }
    });

    // Act
    await dispatch(hideCardDetails());

    // Assert
    expect(getCardDetails(getState())).toBeNull();
  });
});