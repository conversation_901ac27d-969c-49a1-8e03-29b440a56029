import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";
import {addCardToDeck} from "@/src/client/application/commands/addCardToDeck/addCardToDeck";
import {FakeDeckDraftService} from "@/src/client/specs/helpers/fakes/FakeDeckDraftService";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/specs/helpers/factories/fakeCatalogCards";
import {catalogCardsLoadingStartedEvent} from "@/src/client/domain/Catalog/catalogEvents";

describe('deck draft persistence', () => {
  describe('When the deck changes', () => {
    it('should save the draft to the service', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService();
      const {dispatch} = createTestingStore({
        catalog: {cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED}},
      }, {deckDraftService, withDraftSubscriber: true});

      // Act
      await dispatch(addCardToDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

      // Assert
      expect(deckDraftService.loadDraft()).toEqual({
        name: null,
        cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 1}],
      });
    });
  });

  describe('When another action happens before the draft loads', () => {
    it('should keep the existing draft', () => {
      // Arrange
      const existingDraft = {
        name: 'Draft',
        cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}],
      };
      const deckDraftService = new FakeDeckDraftService(existingDraft);
      const {dispatch} = createTestingStore(
        {
          catalog: {cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED}},
        },
        {deckDraftService, withDraftSubscriber: true},
      );

      // Act
      dispatch(catalogCardsLoadingStartedEvent());

      // Assert
      expect(deckDraftService.loadDraft()).toEqual(existingDraft);
    });
  });
});
