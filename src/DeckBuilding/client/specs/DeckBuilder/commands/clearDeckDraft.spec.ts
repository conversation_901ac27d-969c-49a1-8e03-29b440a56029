import {createTestingStore} from '@/src/client/specs/helpers/store/createTestingStore';
import {clearDeckDraft} from '@/src/client/application/commands/clearDeckDraft/clearDeckDraft';
import {FakeDeckDraftService} from '@/src/client/specs/helpers/fakes/FakeDeckDraftService';

describe('clearDeckDraft', () => {
  describe('When a draft exists', () => {
    it('should remove it from the service', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService({name: 'draft', cards: []});
      const {dispatch} = createTestingStore({}, {deckDraftService});

      // Act
      await dispatch(clearDeckDraft());

      // Assert
      expect(deckDraftService.hasDraft()).toBeFalse();
    });
  });
});
