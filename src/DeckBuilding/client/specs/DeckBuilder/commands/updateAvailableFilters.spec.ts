import {updateAvailableFilters} from "@/src/client/application/commands/updateAvailableFilters/updateAvailableFilters";
import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";

describe('updateAvailableFilters', () => {
  it('should update the available filters', async () => {
    // Arrange
    const {dispatch, getState} = createTestingStore();

    // Act
    updateAvailableFilters({
      filters: [
        {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
        {id: '2', name: 'CHARACTER', text: 'Character', dataProperty: 'type', dataType: 'string', value: 'CHARACTER', order: 2},
        {id: '3', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 3},
      ],
    })(dispatch);

    // Assert
    expect(getState().catalogFilters.available).toEqual([
      {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1},
      {id: '2', name: 'CHARACTER', text: 'Character', dataProperty: 'type', dataType: 'string', value: 'CHARACTER', order: 2},
      {id: '3', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 3},
    ]);
  });
});