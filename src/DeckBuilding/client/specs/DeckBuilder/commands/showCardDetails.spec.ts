import {GOOFY_GROUNDBREAKING_CHEF} from "@/src/client/specs/helpers/factories/fakeCatalogCards";
import {createTestingStore} from "@/src/client/specs/helpers/store/createTestingStore";
import {showCardDetails} from "@/src/client/application/commands/showCardDetails/showCardDetails";
import {getCardDetails} from "@/src/client/application/queries/getCardDetails/getCardDetails";

describe('ShowCardDetails', () => {
  it('should open the dialog', async () => {
    // Arrange
    const {dispatch, getState} = createTestingStore({
      deckBuilder: {
        cardsInDeck: {
          [GOOFY_GROUNDBREAKING_CHEF.id]: {
            card: GOOFY_GROUNDBREAKING_CHEF,
            quantity: 1,
          },
        },
      },
      catalog: {
        cards: {
          [GOOFY_GROUNDBREAKING_CHEF.id]: GOOFY_GROUNDBREAKING_CHEF,
        },
      },
    });

    // Act
    await dispatch(showCardDetails({cardId: GOOFY_GROUNDBREAKING_CHEF.id}));

    // Assert
    expect(getCardDetails(getState())).toEqual({
      cardImage: GOOFY_GROUNDBREAKING_CHEF.image,
      cardName: GOOFY_GROUNDBREAKING_CHEF.name,
    });
  });
});