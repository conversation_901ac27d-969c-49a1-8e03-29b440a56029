import {describe, it, expect, beforeEach} from 'vitest';
import {SaveDeckCommandHandler} from '@/src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler';
import {SaveDeckCommand} from '@/src/server/application/commands/Deck/SaveDeck/SaveDeckCommand';
import {InMemoryDeckRepository} from '@/src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository';
import {Deck} from '@/src/server/domain/Deck/Deck';

let repository: InMemoryDeckRepository;
let handler: SaveDeckCommandHandler;

beforeEach(() => {
  repository = new InMemoryDeckRepository();
  handler = new SaveDeckCommandHandler(repository);
});

describe('SaveDeckCommandHandler', () => {
  describe('When saving a new deck', () => {
    it('should persist the deck and return its id', async () => {
      // Arrange
      const command: SaveDeckCommand = {
        gameId: 'game1',
        playerId: 'player1',
        name: 'My deck',
        cards: [{cardId: 'card1', quantity: 2}],
      };

      // Act
      const id = await handler.handle(command);

      // Assert
      const saved = ((repository as unknown) as {items: Map<string, Deck>}).items.get(id);
      expect(saved?.toSnapshot()).toEqual({
        id,
        gameId: 'game1',
        playerId: 'player1',
        name: 'My deck',
        cards: [{cardId: 'card1', quantity: 2}],
      });
    });

    it('should generate sequential ids', async () => {
      // Arrange
      const command: SaveDeckCommand = {
        gameId: 'game1',
        playerId: 'player1',
        name: 'Deck',
        cards: [],
      };

      // Act
      const first = await handler.handle(command);
      const second = await handler.handle(command);

      // Assert
      expect(first).toBe('d1');
      expect(second).toBe('d2');
    });
  });
});
