import {createFakeGame} from "@/src/server/specs/helpers/createFakeGame";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import schema from "@/convex/schema";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/specs/helpers/createAppUsers";
import {LORCANA} from "@/src/server/specs/helpers/fakes/fakeGames";
import {createFakeDeck} from "@/src/server/specs/helpers/createFakeDeck";
import {api} from "@/convex/_generated/api";

describe('LoadDecksByUserIdAndGameId', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When the user has no deck for the game', () => {
    it('should return an empty list', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);

      // Act
      const result = await loadDecksByUserIdAndGameId(asSophie, lorcanaId);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('When the user has decks for the game', () => {
    it('should return the list of decks', async () => {
      // Arrange
      const lorcanaId = await createFakeGame(LORCANA, asAdmin);
      await createFakeDeck({name: 'Deck 1', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);
      await createFakeDeck({name: 'Deck 2', gameId: lorcanaId, playerId: SOPHIE_APP_USER.appUserId}, asAdmin);

      // Act
      const result = await loadDecksByUserIdAndGameId(asSophie, lorcanaId);

      // Assert
      expect(result[0].name).toBe('Deck 1');
      expect(result[1].name).toBe('Deck 2');
    });
  });

  function loadDecksByUserIdAndGameId(currentUser: TestConvexForDataModel<DataModel>, gameId: string,) {
    return currentUser.query(api.queries.loadDecksByUserIdAndGameId.endpoint, {gameId: gameId as Id<"games">});
  }
});