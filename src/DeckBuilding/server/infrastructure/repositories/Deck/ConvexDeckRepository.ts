import {GenericMutationCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {DeckRepository} from "@/src/server/application/ports/DeckRepository";
import {Deck} from "@/src/server/domain/Deck/Deck";

export class ConvexDeckRepository implements DeckRepository {
  constructor(private readonly ctx: GenericMutationCtx<DataModel>) {}

  async save(deck: Deck) {
    const data = deck.toSnapshot();
    const id = await this.ctx.db.insert('decks', {
      ...data,
      gameId: data.gameId as Id<'games'>,
    });
    return id;
  }
}
