import {DeckRepository} from "@/src/server/application/ports/DeckRepository";
import {Deck, DeckProps} from "@/src/server/domain/Deck/Deck";

export class InMemoryDeckRepository implements DeckRepository {
  private items = new Map<string, Deck>();
  private counter = 0;

  async save(deck: Deck) {
    const props = (deck as unknown as {props: DeckProps}).props;
    if (!props.id) {
      props.id = `d${++this.counter}`;
    }
    this.items.set(props.id, deck);
    return props.id;
  }
}
