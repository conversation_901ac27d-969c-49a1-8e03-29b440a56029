import {FC} from "react";
import {Contain<PERSON>, <PERSON>lex, Heading} from "@radix-ui/themes";
import Match from "@/src/client/infrastructure/components/app/Gaming/Match/Match";
import {Id} from "@/convex/_generated/dataModel";
import MatchConsoleEvents from "@/src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents";
import {LeaveMatchButton} from "@/src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton";
import {loadMatchById} from "@/src/server/application/queries/loadMatchById";
import FinishedMatchPage from "@/src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage";
import ErrorLoadingMatchPage from "@/src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage";

type Props = {
  locale: string;
  matchId: string;
};

const MatchPage: FC<Props> = async ({locale, matchId}) => {
  const {data: match, error} = await loadMatchById({matchId});

  if (match?.status === "finished") {
    return <FinishedMatchPage matchId={matchId} match={match}/>;
  }

  if (error) {
    return <ErrorLoadingMatchPage errorMessage={error}/>;
  }

  return (
    <Container p="3">
      <Flex direction="column" gap="4">
        <Heading size="5">Match in progress</Heading>

        <Match matchId={matchId as Id<"matches">} locale={locale} match={match!}/>

        <MatchConsoleEvents
          matchId={matchId as Id<"matches">}
        />

        <LeaveMatchButton matchId={matchId as Id<"matches">}/>
      </Flex>
    </Container>
  );
};

export default MatchPage;