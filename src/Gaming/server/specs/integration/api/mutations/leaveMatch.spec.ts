import {convexTest, TestConvexForDataModel} from "convex-test";
import schema from "@/convex/schema";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER
} from "@/src/server/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/specs/helpers/createAppUsers";
import {createFakeGame} from "@/src/server/specs/helpers/createFakeGame";
import {GOBELIN_DES_BOIS} from "@/src/server/specs/helpers/fakes/fakeGames";
import {getAllFrom} from "@/src/server/specs/helpers/getAllFrom";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {getAppUserAttribute} from "@/src/server/specs/helpers/getAppUser";
import {MatchAlreadyFinishedError} from "@/src/server/domain/Match/errors/MatchAlreadyFinishedError";

describe('leaveMatch', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;
  let gameId: Id<"games">;
  let matchId: Id<"matches">;

  beforeEach(async () => {
    const test = convexTest(schema);
    asAdmin = test.withIdentity(ADMIN_IDENTITY);
    john = test.withIdentity(JOHN_IDENTITY);

    await createAppUser(asAdmin, JOHN_APP_USER);
    await createAppUser(asAdmin, SOPHIE_APP_USER);
    gameId = await createFakeGame(GOBELIN_DES_BOIS, asAdmin);

    matchId = await asAdmin.run(ctx =>
      ctx.db.insert('matches', {
        gameId,
        players: [JOHN_APP_USER.appUserId, SOPHIE_APP_USER.appUserId],
        status: 'setup',
        createdAt: Date.now(),
      })
    );
  });

  describe('When the player leaves an active match', () => {
    it('should identify the other player as winner', async () => {
      // Act
      await makeJohnLeaveMatch(matchId);

      // Assert
      const matches = await getAllFrom('matches', asAdmin);
      expect(matches[0].winner).toBe(SOPHIE_APP_USER.appUserId);
    });

    it('should reset the both players status to idle', async () => {
      // Act
      await makeJohnLeaveMatch(matchId);

      // Assert
      expect(await getAppUserAttribute(asAdmin, JOHN_APP_USER.appUserId, "status")).toBe("idle");
      expect(await getAppUserAttribute(asAdmin, SOPHIE_APP_USER.appUserId, "status")).toBe("idle");
    });

    it('should mark the match as finished', async () => {
      // Act
      await makeJohnLeaveMatch(matchId);

      // Assert
      const matches = await getAllFrom('matches', asAdmin);
      expect(matches[0].status).toBe("finished");
    });
  });

  describe('When the player tries to leave an already finished match', () => {
    it('should throw an error', async () => {
      // Arrange
      await asAdmin.run((ctx) => ctx.db.patch(matchId, {status: "finished"}));

      // Act
      const leavingMatch = makeJohnLeaveMatch(matchId);

      // Assert
      await expect(leavingMatch).rejects.toThrow(new MatchAlreadyFinishedError());
    });
  });

  async function makeJohnLeaveMatch(matchId: Id<"matches">) {
    await john.mutation(api.mutations.leaveMatch.endpoint, {matchId});
  }
});
