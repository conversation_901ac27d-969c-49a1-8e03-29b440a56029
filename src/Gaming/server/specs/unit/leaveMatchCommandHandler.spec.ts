import {describe, it, expect, beforeEach} from 'vitest';
import {Leave<PERSON>atchCommandHandler} from '@/src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler';
import {MatchAlreadyFinishedError} from '@/src/server/domain/Match/errors/MatchAlreadyFinishedError';
import {MatchNotFoundError} from '@/src/server/domain/Match/errors/MatchNotFoundError';
import {InMemoryMatchRepository} from '@/src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository';
import {Match} from '@/src/server/domain/Match/Match';
import {createFakeContext} from '@/src/server/specs/helpers/fakes/FakeContext';

let context: ReturnType<typeof createFakeContext>;
let repository: InMemoryMatchRepository;
let handler: LeaveMatchCommandHandler;

beforeEach(() => {
  context = createFakeContext();
  repository = new InMemoryMatchRepository();
  handler = new LeaveMatchCommandHandler(context, repository);
});

describe('LeaveMatchCommandHandler', () => {
  describe('When the player leaves an active match', () => {
    it('should finish the match with the opponent as winner', async () => {
      // Arrange
      const match = Match.create({gameId: 'g1', players: ['u1','u2'], status: 'setup'});
      const matchId = await repository.save(match);

      // Act
      await handler.handle({matchId, userId: 'u1'});

      // Assert
      const updated = await repository.findById(matchId);
      expect(updated?.getStatus()).toBe('finished');
      expect(updated?.getWinner()).toBe('u2');
      expect(context.recorded[0]).toMatchObject({
        type: 'match',
        gameId: 'g1',
        id: matchId,
        event: {
          type: 'MatchEnded',
          payload: {winner: 'u2', loser: 'u1', matchId},
        },
      });
    });
  });

  describe('When the match is already finished', () => {
    it('should throw an error', async () => {
      // Arrange
      const match = Match.create({gameId: 'g1', players: ['u1','u2'], status: 'finished'});
      const matchId = await repository.save(match);

      // Act
      const leaving = handler.handle({matchId, userId: 'u1'});

      // Assert
      await expect(leaving).rejects.toThrow(new MatchAlreadyFinishedError());
    });
  });

  describe('When the match does not exist', () => {
    it('should throw MatchNotFoundError', async () => {
      // Act
      const leaving = handler.handle({matchId: 'unknown', userId: 'u1'});

      // Assert
      await expect(leaving).rejects.toThrow(new MatchNotFoundError());
    });
  });
});
