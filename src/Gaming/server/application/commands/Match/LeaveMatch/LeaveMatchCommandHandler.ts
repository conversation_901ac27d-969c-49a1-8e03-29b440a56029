import {LeaveMatchCommand} from "./LeaveMatchCommand";
import {Context} from "@/src/server/application/ports/Context";
import {MatchAlreadyFinishedError} from "@/src/server/domain/Match/errors/MatchAlreadyFinishedError";
import {MatchRepository} from "@/src/server/application/ports/MatchRepository";
import {MatchNotFoundError} from "@/src/server/domain/Match/errors/MatchNotFoundError";
export class LeaveMatchCommandHandler {
  private readonly context: Context;
  private readonly matchRepository: MatchRepository;
  constructor(context: Context, matchRepository: MatchRepository) {
    this.context = context;
    this.matchRepository = matchRepository;
  }

  async handle({matchId, userId}: LeaveMatchCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) {
      throw new MatchNotFoundError();
    }
    if (match.isFinished()) {
      throw new MatchAlreadyFinishedError();
    }

    const winner = match.getOpponentOf(userId);
    match.finishMatchWithWinner(winner);
    await this.matchRepository.save(match);

    await this.context.dispatchMatchEvent(match.getGameId(), match.getId(), {
      type: "MatchEnded",
      payload: {
        winner,
        loser: userId,
        matchId: match.getId(),
      },
    });
  }
}
