import {MatchRepository} from "@/src/server/application/ports/MatchRepository";
import {Match, MatchProps} from "@/src/server/domain/Match/Match";

export class InMemoryMatchRepository implements MatchRepository {
  private items = new Map<string, Match>();
  private counter = 0;

  async save(match: Match) {
    const props = (match as unknown as {props: MatchProps}).props;
    if (props.id && this.items.has(props.id)) {
      this.items.set(props.id, match);
      return props.id;
    }

    const id = `m${++this.counter}`;
    props.id = id;
    this.items.set(id, match);
    return id;
  }

  async findById(id: string): Promise<Match | null> {
    return this.items.get(id) || null;
  }

}
