import {FC} from "react";
import {Flex} from "@radix-ui/themes";
import DeckBuildingFilters from "@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters";
import DeckBuilderCardsGrid from "@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid";
import DeckBuilderPanel from "@/src/DeckBuilding/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";
import {EditDeckInitializer} from "@/src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer";
import {DeckDraftInitializer} from "@/src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer";

type Props = { params: Promise<{ gameId: string; deckId: string; locale: string }> };

const EditDeckPage: FC<Props> = async ({params}) => {
  const {gameId, deckId} = await params;
  const [{ data }, deck] = await Promise.all([
    fetchQuery(api.queries.loadAvailableFilters.endpoint, { gameId: gameId as Id<'games'> }, {token: await convexAuthNextjsToken()}),
    fetchQuery(api.queries.loadDeckById.endpoint, {deckId: deckId as Id<'decks'>}, {token: await convexAuthNextjsToken()})
  ]);

  return (
    <Flex direction="row" gap="1" p="3" className="h-full">
      <Flex direction="column" className="min-w-[240px]">
        <DeckBuildingFilters groupedFilters={data.groupedFilters} availableFilters={data.availableFilters}/>
      </Flex>

      <Flex direction="column" className="h-full w-full" >
        <EditDeckInitializer deck={deck} />
        <DeckDraftInitializer />
        <DeckBuilderCardsGrid/>
      </Flex>

      <Flex direction="column" className="h-full min-w-[400px]">
        <DeckBuilderPanel/>
      </Flex>
    </Flex>
  );
};

export default EditDeckPage;
